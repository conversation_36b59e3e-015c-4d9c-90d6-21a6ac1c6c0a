<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>热门排行榜展示效果</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 320px;
            margin: 0 auto;
        }
        
        /* ==================== 热门排行榜样式 ==================== */
        #toprank-box {
            border: 1px solid #e8e8e8;
            margin-bottom: 15px;
        }

        #toprank-box h3 {
            background: linear-gradient(90deg, #4a90e2, #357abd);
            font-size: 14px;
            padding: 6px;
            margin: 0;
            position: relative;
            color: #fff;
        }

        .toprank-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
            gap: 2px;
            margin: 0;
            padding: 8px;
            list-style: none;
        }

        .toprank-item {
            position: relative;
        }

        .toprank-link {
            display: block;
            background: #fff;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 8px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            min-height: 70px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .toprank-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #ff6b6b;
        }

        .recommend-icon {
            position: relative;
            margin-bottom: 5px;
        }

        .recommend-icon img {
            width: 28px;
            height: 28px;
            border-radius: 4px;
        }

        .toprank-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            font-size: 9px;
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 10px;
            color: #fff;
            text-transform: uppercase;
            z-index: 2;
            min-width: 18px;
            text-align: center;
        }

        /* 前三名特殊样式 */
        .toprank-badge.rank-1 {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
        }

        .toprank-badge.rank-2 {
            background: linear-gradient(135deg, #ffa726, #ff9800);
            box-shadow: 0 2px 8px rgba(255, 167, 38, 0.4);
        }

        .toprank-badge.rank-3 {
            background: linear-gradient(135deg, #66bb6a, #4caf50);
            box-shadow: 0 2px 8px rgba(102, 187, 106, 0.4);
        }

        /* 其他排名样式 */
        .toprank-badge:not(.rank-1):not(.rank-2):not(.rank-3) {
            background: linear-gradient(135deg, #78909c, #607d8b);
            box-shadow: 0 2px 8px rgba(120, 144, 156, 0.4);
        }

        .recommend-name {
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            margin-bottom: 3px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
        }

        .toprank-stats {
            font-size: 10px;
            color: #999;
            margin-top: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 3px;
        }

        .rank-views {
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .rank-views i {
            font-size: 9px;
        }

        /* 响应式适配 */
        @media (max-width: 767px) {
            .toprank-list {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
                gap: 6px;
            }

            .toprank-link {
                padding: 8px 6px;
                min-height: 50px;
            }

            .recommend-name {
                font-size: 10px;
            }

            .toprank-stats {
                font-size: 9px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 热门排行榜 -->
        <div id="toprank-box" class="clearfix">
            <h3>
                <span style="color: #ff6b6b; font-weight: bold; text-shadow: 0 1px 2px rgba(255, 107, 107, 0.3); font-size: 14px;">
                    <i class="fas fa-trophy" style="color: #ffd700; margin-right: 5px;"></i>热门排行榜
                </span>
                <a href="?mod=top" style="color: #fff; font-size: 12px; text-decoration: none; float: right; opacity: 0.8;" title="查看完整排行榜">更多 &raquo;</a>
            </h3>
            <ul class="clearfix toprank-list">
                <!-- 模拟数据 -->
                <li class="recommend-item toprank-item">
                    <a href="#" title="百度" class="recommend-link toprank-link">
                        <div class="recommend-icon">
                            <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://baidu.com"
                                 width="28" height="28" alt="百度网站图标" loading="lazy" />
                            <div class="recommend-badge toprank-badge rank-1">
                                <i class="fas fa-trophy"></i>1
                            </div>
                        </div>
                        <span class="recommend-name">百度</span>
                        <div class="toprank-stats">
                            <span class="rank-views">
                                <i class="fas fa-eye"></i>12580
                            </span>
                        </div>
                    </a>
                </li>
                
                <li class="recommend-item toprank-item">
                    <a href="#" title="腾讯" class="recommend-link toprank-link">
                        <div class="recommend-icon">
                            <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://qq.com"
                                 width="28" height="28" alt="腾讯网站图标" loading="lazy" />
                            <div class="recommend-badge toprank-badge rank-2">
                                <i class="fas fa-trophy"></i>2
                            </div>
                        </div>
                        <span class="recommend-name">腾讯</span>
                        <div class="toprank-stats">
                            <span class="rank-views">
                                <i class="fas fa-eye"></i>9876
                            </span>
                        </div>
                    </a>
                </li>
                
                <li class="recommend-item toprank-item">
                    <a href="#" title="阿里巴巴" class="recommend-link toprank-link">
                        <div class="recommend-icon">
                            <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://alibaba.com"
                                 width="28" height="28" alt="阿里巴巴网站图标" loading="lazy" />
                            <div class="recommend-badge toprank-badge rank-3">
                                <i class="fas fa-trophy"></i>3
                            </div>
                        </div>
                        <span class="recommend-name">阿里巴巴</span>
                        <div class="toprank-stats">
                            <span class="rank-views">
                                <i class="fas fa-eye"></i>8765
                            </span>
                        </div>
                    </a>
                </li>
                
                <li class="recommend-item toprank-item">
                    <a href="#" title="京东" class="recommend-link toprank-link">
                        <div class="recommend-icon">
                            <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://jd.com"
                                 width="28" height="28" alt="京东网站图标" loading="lazy" />
                            <div class="recommend-badge toprank-badge rank-4">4</div>
                        </div>
                        <span class="recommend-name">京东</span>
                        <div class="toprank-stats">
                            <span class="rank-views">
                                <i class="fas fa-eye"></i>7654
                            </span>
                        </div>
                    </a>
                </li>
                
                <li class="recommend-item toprank-item">
                    <a href="#" title="网易" class="recommend-link toprank-link">
                        <div class="recommend-icon">
                            <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://163.com"
                                 width="28" height="28" alt="网易网站图标" loading="lazy" />
                            <div class="recommend-badge toprank-badge rank-5">5</div>
                        </div>
                        <span class="recommend-name">网易</span>
                        <div class="toprank-stats">
                            <span class="rank-views">
                                <i class="fas fa-eye"></i>6543
                            </span>
                        </div>
                    </a>
                </li>
                
                <li class="recommend-item toprank-item">
                    <a href="#" title="新浪" class="recommend-link toprank-link">
                        <div class="recommend-icon">
                            <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://sina.com"
                                 width="28" height="28" alt="新浪网站图标" loading="lazy" />
                            <div class="recommend-badge toprank-badge rank-6">6</div>
                        </div>
                        <span class="recommend-name">新浪</span>
                        <div class="toprank-stats">
                            <span class="rank-views">
                                <i class="fas fa-eye"></i>5432
                            </span>
                        </div>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</body>
</html>
